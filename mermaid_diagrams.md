# GD32F470系统架构Mermaid图表

## 1. 系统整体架构图

```mermaid
graph TD
    %% 应用层
    A[系统主控 main.c] --> B[任务调度器 scheduler]
    
    %% 应用任务层
    B --> C1[ADC采集任务]
    B --> C2[按键处理任务]
    B --> C3[串口通信任务]
    B --> C4[RTC时钟任务]
    
    %% 硬件抽象层
    C1 --> D1[BSP硬件抽象层]
    C2 --> D1
    C3 --> D1
    C4 --> D1
    
    %% 底层驱动
    D1 --> E1[ADC驱动]
    D1 --> E2[GPIO驱动]
    D1 --> E3[USART驱动]
    D1 --> E4[Timer驱动]
    D1 --> E5[I2C驱动]
    D1 --> E6[SPI驱动]
    D1 --> E7[SDIO驱动]
    
    %% 外设组件
    E1 --> F1[电压采集]
    E2 --> F2[LED指示]
    E2 --> F3[按键输入]
    E3 --> F4[串口通信]
    E4 --> F5[定时采样]
    E5 --> F6[OLED显示]
    E6 --> F7[Flash存储]
    E7 --> F8[SD卡存储]
    
    %% 数据流
    C1 -.数据.-> G1[数据处理]
    G1 --> G2[阈值检测]
    G1 --> G3[格式转换]
    G2 --> G4[超限报警]
    G3 --> G5[数据存储]
    G5 --> F7
    G5 --> F8
    
    %% 样式定义
    classDef appLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef taskLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef bspLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef driverLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef deviceLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataFlow fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class A appLayer
    class B appLayer
    class C1,C2,C3,C4 taskLayer
    class D1 bspLayer
    class E1,E2,E3,E4,E5,E6,E7 driverLayer
    class F1,F2,F3,F4,F5,F6,F7,F8 deviceLayer
    class G1,G2,G3,G4,G5 dataFlow
```

## 2. 模块接口设计图

```mermaid
graph LR
    %% 核心控制模块
    MAIN[主控制器<br/>main.c] --> SCHED[调度器<br/>scheduler]
    
    %% 应用模块
    SCHED --> ADC[ADC应用<br/>adc_app]
    SCHED --> BTN[按键应用<br/>btn_app]
    SCHED --> UART[串口应用<br/>usart_app]
    SCHED --> RTC[时钟应用<br/>rtc_app]
    
    %% 硬件抽象层
    ADC --> BSP[BSP抽象层<br/>bsp_gd32f470vet6]
    BTN --> BSP
    UART --> BSP
    RTC --> BSP
    
    %% 存储模块
    ADC --> FLASH[Flash存储<br/>flash_app]
    ADC --> SD[SD卡存储<br/>sd_app]
    UART --> FLASH
    UART --> SD
    
    %% 通信组件
    UART --> RING[环形缓冲区<br/>uart_ringbuffer]
    
    %% 显示模块
    ADC --> OLED[OLED显示<br/>oled_app]
    
    %% 接口标注
    SCHED -.任务函数指针.-> ADC
    SCHED -.定时调度.-> BTN
    ADC -.标志位通信.-> UART
    BTN -.全局变量.-> ADC
    
    %% 样式定义
    classDef coreModule fill:#bbdefb,stroke:#1976d2,stroke-width:3px
    classDef appModule fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef bspModule fill:#ffecb3,stroke:#f57c00,stroke-width:2px
    classDef storageModule fill:#f8bbd9,stroke:#c2185b,stroke-width:2px
    classDef commModule fill:#d1c4e9,stroke:#7b1fa2,stroke-width:2px
    
    class MAIN,SCHED coreModule
    class ADC,BTN,UART,RTC appModule
    class BSP bspModule
    class FLASH,SD,OLED storageModule
    class RING commModule
```

## 3. 模块接口交互流程图

```mermaid
sequenceDiagram
    participant M as 主控制器
    participant S as 调度器
    participant B as 按键模块
    participant A as ADC模块
    participant U as 串口模块
    participant F as Flash模块
    participant SD as SD卡模块
    
    M->>S: 系统初始化
    S->>S: 任务注册
    
    loop 主循环
        S->>B: 调用btn_task()
        B->>B: 检测按键状态
        alt 按键按下
            B->>A: 设置pre_KeyNum标志
        end
        
        S->>A: 调用adc_flip_task()
        A->>A: 检查pre_KeyNum标志
        alt 启动采样
            A->>A: 启动定时器
            Note over A: timer6_execute_flag=1
        end
        
        S->>A: 调用adc_process_task()
        A->>A: 检查timer6_execute_flag
        alt 需要处理数据
            A->>A: ADC数据转换
            A->>F: 存储配置参数
            A->>SD: 存储采样数据
            A->>U: 输出串口数据
        end
        
        S->>U: 调用uart_task()
        U->>U: 处理串口命令
        alt 配置命令
            U->>F: 更新Flash配置
            U->>SD: 记录操作日志
        end
    end
```

## 4. 数据流图

```mermaid
flowchart TD
    A[电压输入] --> B[ADC采集]
    B --> C{数据处理}
    C --> D[变比计算]
    D --> E{阈值检测}
    E -->|正常| F[正常数据存储]
    E -->|超限| G[超限数据存储]
    E -->|超限| H[LED报警]
    F --> I[SD卡/sample]
    G --> J[SD卡/overLimit]
    C --> K{加密模式?}
    K -->|是| L[数据加密]
    K -->|否| M[明文输出]
    L --> N[SD卡/hideData]
    M --> O[串口输出]
    L --> P[加密串口输出]
    
    style A fill:#e3f2fd
    style H fill:#ffebee
    style I fill:#e8f5e8
    style J fill:#fff3e0
    style N fill:#f3e5f5
```

## 5. 系统状态图

```mermaid
stateDiagram-v2
    [*] --> 系统初始化
    系统初始化 --> 待机状态
    待机状态 --> 采样状态 : 按键/命令启动
    采样状态 --> 待机状态 : 按键/命令停止
    采样状态 --> 数据处理 : 定时器触发
    数据处理 --> 正常存储 : 数据正常
    数据处理 --> 超限处理 : 数据超限
    正常存储 --> 采样状态
    超限处理 --> 采样状态
    
    待机状态 --> 配置模式 : 配置命令
    配置模式 --> 待机状态 : 配置完成
    
    采样状态 --> 加密模式 : hide命令
    加密模式 --> 采样状态 : unhide命令
```
