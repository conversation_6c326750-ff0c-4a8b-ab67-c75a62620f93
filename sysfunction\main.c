
#include "bsp_gd32f470vet6.h"

int main(void)
{
	

    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_usart_init();
	
    bsp_adc_init();
//    bsp_dac_init();
    sd_fatfs_init();
//    app_btn_init();
    OLED_Init();
//    test_spi_flash();
//    sd_fatfs_test();
		flash_stored();
	flash_ratio_threshold_init();
	power_on_count_init();  // 初始化开机计数器
    scheduler_init();
		all_timer_init();

		my_printf(DEBUG_USART, "====system init====\n");

		// 记录系统初始化
		store_log_entry("system init");

		//打印队伍编号
		my_printf(DEBUG_USART, "Device_ID:2025-CIMC-");
		flash_read_direct(Device_ID_ADDR,20);

		// 记录硬件测试
		store_log_entry("system hardware test");

		// 检查SD卡状态
		if (sd_init_success) {
			store_log_entry("test ok");
		} else {
			store_log_entry("test error: tf card not found");
		}

		my_printf(DEBUG_USART, "\n====system ready====\n");
		

		
    while(1) {
			
      
        
        scheduler_run();
    }
}


int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}

