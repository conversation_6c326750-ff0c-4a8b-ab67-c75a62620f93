# GD32F470定时器调用执行流程分析

## 项目概述

本文档详细分析了GD32F470数据采集系统中定时器的调用执行流程，包括定时器初始化、中断处理、任务调度等关键环节。

## 1. 定时器整体调用执行流程图

```mermaid
graph TD
    %% 系统启动流程
    A[系统启动 main] --> B[all_timer_init]
    B --> C1[timer4_int_init<br/>500ms周期]
    B --> C2[timer6_int_init<br/>5s周期]
    
    %% 定时器初始化详细流程
    C1 --> D1[使能TIMER4时钟<br/>rcu_periph_clock_enable]
    C1 --> D2[复位TIMER4<br/>timer_deinit]
    C1 --> D3[配置定时器参数<br/>timer_init]
    C1 --> D4[使能中断<br/>timer_interrupt_enable]
    C1 --> D5[配置NVIC<br/>nvic_irq_enable]
    C1 --> D6[初始状态禁用<br/>timer_disable]
    
    C2 --> E1[使能TIMER6时钟<br/>rcu_periph_clock_enable]
    C2 --> E2[复位TIMER6<br/>timer_deinit]
    C2 --> E3[配置定时器参数<br/>timer_init]
    C2 --> E4[使能中断<br/>timer_interrupt_enable]
    C2 --> E5[配置NVIC<br/>nvic_irq_enable]
    C2 --> E6[初始状态禁用<br/>timer_disable]
    
    %% 系统调度器
    A --> F[scheduler_init]
    A --> G[主循环 while(1)]
    G --> H[scheduler_run]
    H --> I[get_system_ms<br/>获取系统时间]
    H --> J{检查任务执行时间}
    J -->|时间到| K[执行任务函数]
    J -->|时间未到| H
    
    %% 任务执行流程
    K --> L1[adc_flip_task<br/>5ms周期]
    K --> L2[btn_task<br/>5ms周期]
    K --> L3[uart_task<br/>5ms周期]
    K --> L4[rtc_task<br/>50ms周期]
    K --> L5[adc_process_task<br/>1ms周期]
    
    %% 定时器启动流程
    L1 --> M{检查pre_KeyNum标志}
    M -->|按键触发| N[启动定时器]
    N --> N1[timer_enable TIMER4<br/>LED闪烁指示]
    N --> N2[timer_enable TIMER6<br/>ADC采样定时]
    
    L2 --> O[检测按键状态]
    O -->|按键按下| P[设置pre_KeyNum=1]
    
    L3 --> Q[处理串口命令]
    Q -->|START命令| N
    Q -->|STOP命令| R[停止定时器]
    R --> R1[timer_disable TIMER4]
    R --> R2[timer_disable TIMER6]
    
    %% 定时器中断处理
    N1 --> S1[TIMER4中断触发<br/>500ms]
    N2 --> S2[TIMER6中断触发<br/>5s基础周期]
    
    S1 --> T1[TIMER4_IRQHandler]
    T1 --> T2[检查中断标志<br/>timer_interrupt_flag_get]
    T2 --> T3[清除中断标志<br/>timer_interrupt_flag_clear]
    T3 --> T4[LED状态切换<br/>LED1闪烁]
    
    S2 --> U1[TIMER6_IRQHandler]
    U1 --> U2[检查中断标志<br/>timer_interrupt_flag_get]
    U2 --> U3[清除中断标志<br/>timer_interrupt_flag_clear]
    U3 --> U4{根据KeyNum判断执行频率}
    U4 -->|KeyNum=2| U5[每个周期执行<br/>5s采样]
    U4 -->|KeyNum=3| U6[每2个周期执行<br/>10s采样]
    U4 -->|KeyNum=4| U7[每3个周期执行<br/>15s采样]
    U5 --> V[设置timer6_execute_flag=1]
    U6 --> V
    U7 --> V
    
    %% ADC数据处理
    L5 --> W{检查timer6_execute_flag}
    W -->|标志位=1| X[执行ADC数据处理]
    W -->|标志位=0| H
    X --> X1[读取ADC值]
    X --> X2[电压转换]
    X --> X3[阈值检测]
    X --> X4[数据存储]
    X --> X5[串口输出]
    X --> Y[清除timer6_execute_flag=0]
    
    %% SysTick系统定时器
    Z[SysTick定时器<br/>1ms中断] --> Z1[SysTick_Handler]
    Z1 --> Z2[delay_decrement<br/>延时计数]
    Z --> Z3[get_system_ms<br/>系统时间基准]
    Z3 --> I
    
    %% 样式定义
    classDef initModule fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef timerModule fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef taskModule fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef interruptModule fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef controlModule fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef systickModule fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    class A,B,F initModule
    class C1,C2,D1,D2,D3,D4,D5,D6,E1,E2,E3,E4,E5,E6 timerModule
    class G,H,I,J,K,L1,L2,L3,L4,L5 taskModule
    class S1,S2,T1,T2,T3,T4,U1,U2,U3,U4,U5,U6,U7,V interruptModule
    class M,N,N1,N2,O,P,Q,R,R1,R2,W,X,X1,X2,X3,X4,X5,Y controlModule
    class Z,Z1,Z2,Z3 systickModule
```

## 2. 定时器执行时序图

```mermaid
sequenceDiagram
    participant M as 主程序
    participant S as 调度器
    participant B as 按键任务
    participant A as ADC任务
    participant T4 as TIMER4
    participant T6 as TIMER6
    participant ST as SysTick
    participant ADC as ADC处理
    
    Note over M: 系统启动
    M->>M: all_timer_init()
    M->>T4: timer4_int_init(500ms)
    M->>T6: timer6_int_init(5s)
    Note over T4,T6: 定时器初始化完成，但处于禁用状态
    
    M->>S: scheduler_init()
    
    loop 主循环
        M->>S: scheduler_run()
        S->>ST: get_system_ms()
        ST-->>S: 返回当前时间
        
        alt 按键任务时间到(5ms)
            S->>B: btn_task()
            B->>B: 检测按键状态
            alt 按键按下
                B->>A: 设置pre_KeyNum=1
            end
        end
        
        alt ADC控制任务时间到(5ms)
            S->>A: adc_flip_task()
            A->>A: 检查pre_KeyNum标志
            alt 启动采样
                A->>T4: timer_enable(TIMER4)
                A->>T6: timer_enable(TIMER6)
                Note over T4,T6: 定时器开始运行
            end
            alt 停止采样
                A->>T4: timer_disable(TIMER4)
                A->>T6: timer_disable(TIMER6)
                Note over T4,T6: 定时器停止运行
            end
        end
        
        alt ADC处理任务时间到(1ms)
            S->>ADC: adc_process_task()
            ADC->>ADC: 检查timer6_execute_flag
            alt 标志位为1
                ADC->>ADC: 执行ADC数据处理
                ADC->>ADC: 清除timer6_execute_flag=0
            end
        end
    end
    
    par 定时器中断并行执行
        loop TIMER4中断(500ms周期)
            T4->>T4: TIMER4_IRQHandler()
            T4->>T4: 检查并清除中断标志
            T4->>T4: LED1状态切换
        end
    and
        loop TIMER6中断(5s基础周期)
            T6->>T6: TIMER6_IRQHandler()
            T6->>T6: 检查并清除中断标志
            T6->>T6: 根据KeyNum计算执行频率
            alt 应该执行
                T6->>ADC: 设置timer6_execute_flag=1
            end
        end
    and
        loop SysTick中断(1ms周期)
            ST->>ST: SysTick_Handler()
            ST->>ST: delay_decrement()
            ST->>ST: 更新系统时间计数
        end
    end
```

## 3. 定时器配置参数详解

```mermaid
graph LR
    %% 定时器配置参数
    A[定时器配置参数] --> B[TIMER4配置]
    A --> C[TIMER6配置]
    A --> D[SysTick配置]

    %% TIMER4详细配置
    B --> B1[ARR = 4999-1<br/>自动重装载值]
    B --> B2[PSC = 11999-1<br/>预分频值]
    B --> B3[时钟源 = APB1<br/>120MHz]
    B --> B4[计算周期:<br/>500ms]
    B --> B5[功能: LED闪烁指示]

    %% TIMER6详细配置
    C --> C1[ARR = 49999-1<br/>自动重装载值]
    C --> C2[PSC = 11999-1<br/>预分频值]
    C --> C3[时钟源 = APB1<br/>120MHz]
    C --> C4[基础周期: 5s]
    C --> C5[功能: ADC采样触发]

    %% SysTick配置
    D --> D1[重装载值<br/>SystemCoreClock/1000]
    D --> D2[时钟源 = 系统时钟<br/>240MHz]
    D --> D3[中断周期: 1ms]
    D --> D4[功能: 系统时基]

    %% 周期计算公式
    E[定时器周期计算公式] --> F[Tout = ((ARR+1) × (PSC+1)) / Ft]
    F --> F1[Ft = 定时器工作频率]
    F --> F2[TIMER4: ((4999+1) × (11999+1)) / 120MHz = 500ms]
    F --> F3[TIMER6: ((49999+1) × (11999+1)) / 120MHz = 5000ms]

    %% 采样周期控制
    G[采样周期控制机制] --> H[KeyNum值控制]
    H --> H1[KeyNum=2: 5s采样<br/>每个TIMER6周期执行]
    H --> H2[KeyNum=3: 10s采样<br/>每2个TIMER6周期执行]
    H --> H3[KeyNum=4: 15s采样<br/>每3个TIMER6周期执行]

    %% 中断优先级
    I[中断优先级配置] --> J[TIMER4: 抢占优先级1, 响应优先级3]
    I --> K[TIMER6: 抢占优先级2, 响应优先级1]
    I --> L[SysTick: 优先级0 (最高)]

    %% 样式定义
    classDef configNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef timer4Node fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef timer6Node fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef systickNode fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef formulaNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef controlNode fill:#e0f2f1,stroke:#00695c,stroke-width:2px

    class A,E,G,I configNode
    class B,B1,B2,B3,B4,B5,J timer4Node
    class C,C1,C2,C3,C4,C5,K timer6Node
    class D,D1,D2,D3,D4,L systickNode
    class F,F1,F2,F3 formulaNode
    class H,H1,H2,H3 controlNode
```

## 4. 定时器调用流程详细分析

### 4.1 系统启动阶段

**初始化流程：**
1. **main函数启动** → 调用`all_timer_init()`
2. **TIMER4初始化** → `timer4_int_init(4999-1, 11999-1)`
   - 使能时钟：`rcu_periph_clock_enable(RCU_TIMER4)`
   - 复位定时器：`timer_deinit(TIMER4)`
   - 配置参数：预分频、周期、计数方向等
   - 使能中断：`timer_interrupt_enable(TIMER4, TIMER_INT_UP)`
   - 配置NVIC：`nvic_irq_enable(TIMER4_IRQn, 1, 3)`
   - **初始状态禁用**：`timer_disable(TIMER4)`

3. **TIMER6初始化** → `timer6_int_init(49999-1, 11999-1)`
   - 类似TIMER4的初始化流程
   - **初始状态禁用**：`timer_disable(TIMER6)`

### 4.2 任务调度阶段

**调度器运行机制：**
1. **主循环** → 持续调用`scheduler_run()`
2. **时间获取** → `get_system_ms()`基于SysTick计数
3. **任务检查** → 遍历任务数组，检查执行时间
4. **任务执行** → 调用对应的任务函数

**关键任务周期：**
- `adc_flip_task`: 5ms周期，控制定时器启停
- `btn_task`: 5ms周期，检测按键状态
- `uart_task`: 5ms周期，处理串口命令
- `adc_process_task`: 1ms周期，处理ADC数据
- `rtc_task`: 50ms周期，更新时间显示

### 4.3 定时器控制阶段

**启动条件：**
- 按键触发：`btn_task`设置`pre_KeyNum=1`
- 串口命令：`uart_task`接收"START"命令

**启动流程：**
1. `adc_flip_task`检查`pre_KeyNum`标志
2. 调用`timer_enable(TIMER4)`启动LED闪烁
3. 调用`timer_enable(TIMER6)`启动ADC采样定时

**停止流程：**
1. 按键或"STOP"命令触发
2. 调用`timer_disable(TIMER4)`停止LED闪烁
3. 调用`timer_disable(TIMER6)`停止ADC采样

### 4.4 中断处理阶段

**TIMER4中断处理：**
```c
void TIMER4_IRQHandler(void) {
    if (timer_interrupt_flag_get(TIMER4, TIMER_INT_FLAG_UP) == SET) {
        timer_interrupt_flag_clear(TIMER4, TIMER_INT_FLAG_UP);
        // LED1状态切换逻辑
        LED1_TOGGLE;
    }
}
```

**TIMER6中断处理：**
```c
void TIMER6_IRQHandler(void) {
    if (timer_interrupt_flag_get(TIMER6, TIMER_INT_FLAG_UP) == SET) {
        timer_interrupt_flag_clear(TIMER6, TIMER_INT_FLAG_UP);
        // 根据KeyNum值控制执行频率
        if (should_execute) {
            timer6_execute_flag = 1;  // 设置ADC处理标志
        }
    }
}
```

### 4.5 数据处理阶段

**ADC数据处理流程：**
1. `adc_process_task`检查`timer6_execute_flag`
2. 标志位为1时执行ADC数据处理
3. 读取ADC值并进行电压转换
4. 执行阈值检测和数据存储
5. 清除`timer6_execute_flag=0`

## 5. 关键设计特点

### 5.1 分离式设计
- **定时器初始化**与**定时器控制**分离
- 初始化时定时器处于禁用状态
- 通过任务调度动态控制定时器启停

### 5.2 标志位通信
- 使用`pre_KeyNum`标志控制定时器启停
- 使用`timer6_execute_flag`标志触发ADC处理
- 避免了中断中的复杂处理逻辑

### 5.3 可变采样周期
- TIMER6提供5s基础周期
- 通过KeyNum值实现5s/10s/15s可变采样
- 中断中只设置标志位，主任务中处理数据

### 5.4 多层次时间管理
- **SysTick**: 1ms系统时基
- **任务调度**: 1-50ms任务周期
- **定时器中断**: 500ms-5s应用周期

---

**文档生成时间：** 2025年6月19日
**项目团队：** 2025815992 安徽工业大学
**开发环境：** Keil MDK 5.06 + GD32F4xx标准库
