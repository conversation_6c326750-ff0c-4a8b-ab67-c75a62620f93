# GD32F470定时器调用执行流程分析

## 项目概述

本文档详细分析了GD32F470数据采集系统中定时器的调用执行流程，包括定时器初始化、中断处理、任务调度等关键环节。

## 1. 系统初始化流程

```mermaid
flowchart TD
    A[系统启动 main] --> B[all_timer_init]
    B --> C1[timer4_int_init<br/>500ms周期]
    B --> C2[timer6_int_init<br/>5s周期]

    C1 --> D1[配置TIMER4<br/>LED闪烁指示]
    C2 --> D2[配置TIMER6<br/>ADC采样触发]

    D1 --> E[初始状态禁用]
    D2 --> E

    A --> F[scheduler_init<br/>任务调度初始化]
    F --> G[进入主循环]
```

## 2. 任务调度流程

```mermaid
flowchart TD
    A[scheduler_run] --> B[get_system_ms<br/>获取当前时间]
    B --> C{检查各任务<br/>执行时间}

    C -->|1ms周期| D[adc_process_task<br/>ADC数据处理]
    C -->|5ms周期| E[adc_flip_task<br/>定时器控制]
    C -->|5ms周期| F[btn_task<br/>按键检测]
    C -->|5ms周期| G[uart_task<br/>串口处理]
    C -->|50ms周期| H[rtc_task<br/>时钟显示]

    D --> I[返回调度器]
    E --> I
    F --> I
    G --> I
    H --> I
    I --> A
```

## 3. 定时器控制流程

```mermaid
flowchart TD
    A[按键按下或START命令] --> B[设置pre_KeyNum=1]
    B --> C[adc_flip_task检查标志]
    C --> D{标志位检查}

    D -->|启动| E[timer_enable TIMER4<br/>timer_enable TIMER6]
    D -->|停止| F[timer_disable TIMER4<br/>timer_disable TIMER6]

    E --> G[定时器开始运行]
    F --> H[定时器停止运行]
```

## 4. 中断处理流程

```mermaid
flowchart TD
    A[TIMER4中断<br/>500ms] --> B[TIMER4_IRQHandler]
    B --> C[LED状态切换]

    D[TIMER6中断<br/>5s基础周期] --> E[TIMER6_IRQHandler]
    E --> F{KeyNum值判断}
    F -->|KeyNum=2| G[每周期执行<br/>5s采样]
    F -->|KeyNum=3| H[每2周期执行<br/>10s采样]
    F -->|KeyNum=4| I[每3周期执行<br/>15s采样]

    G --> J[设置timer6_execute_flag=1]
    H --> J
    I --> J

    K[SysTick中断<br/>1ms] --> L[系统时间更新<br/>delay_decrement]
```

## 5. ADC数据处理流程

```mermaid
flowchart TD
    A[adc_process_task] --> B{检查timer6_execute_flag}
    B -->|标志位=0| C[返回调度器]
    B -->|标志位=1| D[执行ADC处理]

    D --> E[读取ADC值]
    E --> F[电压转换]
    F --> G[阈值检测]
    G --> H[数据存储]
    H --> I[串口输出]
    I --> J[清除timer6_execute_flag=0]
    J --> C
```

## 6. 定时器执行时序图

```mermaid
sequenceDiagram
    participant M as 主程序
    participant S as 调度器
    participant B as 按键任务
    participant A as ADC任务
    participant T4 as TIMER4
    participant T6 as TIMER6
    participant ST as SysTick
    participant ADC as ADC处理
    
    Note over M: 系统启动
    M->>M: all_timer_init()
    M->>T4: timer4_int_init(500ms)
    M->>T6: timer6_int_init(5s)
    Note over T4,T6: 定时器初始化完成，但处于禁用状态
    
    M->>S: scheduler_init()
    
    loop 主循环
        M->>S: scheduler_run()
        S->>ST: get_system_ms()
        ST-->>S: 返回当前时间
        
        alt 按键任务时间到(5ms)
            S->>B: btn_task()
            B->>B: 检测按键状态
            alt 按键按下
                B->>A: 设置pre_KeyNum=1
            end
        end
        
        alt ADC控制任务时间到(5ms)
            S->>A: adc_flip_task()
            A->>A: 检查pre_KeyNum标志
            alt 启动采样
                A->>T4: timer_enable(TIMER4)
                A->>T6: timer_enable(TIMER6)
                Note over T4,T6: 定时器开始运行
            end
            alt 停止采样
                A->>T4: timer_disable(TIMER4)
                A->>T6: timer_disable(TIMER6)
                Note over T4,T6: 定时器停止运行
            end
        end
        
        alt ADC处理任务时间到(1ms)
            S->>ADC: adc_process_task()
            ADC->>ADC: 检查timer6_execute_flag
            alt 标志位为1
                ADC->>ADC: 执行ADC数据处理
                ADC->>ADC: 清除timer6_execute_flag=0
            end
        end
    end
    
    par 定时器中断并行执行
        loop TIMER4中断(500ms周期)
            T4->>T4: TIMER4_IRQHandler()
            T4->>T4: 检查并清除中断标志
            T4->>T4: LED1状态切换
        end
    and
        loop TIMER6中断(5s基础周期)
            T6->>T6: TIMER6_IRQHandler()
            T6->>T6: 检查并清除中断标志
            T6->>T6: 根据KeyNum计算执行频率
            alt 应该执行
                T6->>ADC: 设置timer6_execute_flag=1
            end
        end
    and
        loop SysTick中断(1ms周期)
            ST->>ST: SysTick_Handler()
            ST->>ST: delay_decrement()
            ST->>ST: 更新系统时间计数
        end
    end
```

## 7. 定时器配置参数详解

```mermaid
flowchart LR
    A[定时器配置参数] --> B[TIMER4配置]
    A --> C[TIMER6配置]
    A --> D[SysTick配置]

    B --> B1[ARR = 4999-1 自动重装载值]
    B --> B2[PSC = 11999-1 预分频值]
    B --> B3[时钟源 = APB1 120MHz]
    B --> B4[计算周期: 500ms]
    B --> B5[功能: LED闪烁指示]

    C --> C1[ARR = 49999-1 自动重装载值]
    C --> C2[PSC = 11999-1 预分频值]
    C --> C3[时钟源 = APB1 120MHz]
    C --> C4[基础周期: 5s]
    C --> C5[功能: ADC采样触发]

    D --> D1[重装载值 SystemCoreClock/1000]
    D --> D2[时钟源 = 系统时钟 240MHz]
    D --> D3[中断周期: 1ms]
    D --> D4[功能: 系统时基]

    E[定时器周期计算公式] --> F[Tout = ARR+1 × PSC+1 / Ft]
    F --> F1[Ft = 定时器工作频率]
    F --> F2[TIMER4: 4999+1 × 11999+1 / 120MHz = 500ms]
    F --> F3[TIMER6: 49999+1 × 11999+1 / 120MHz = 5000ms]

    G[采样周期控制机制] --> H[KeyNum值控制]
    H --> H1[KeyNum=2: 5s采样 每个TIMER6周期执行]
    H --> H2[KeyNum=3: 10s采样 每2个TIMER6周期执行]
    H --> H3[KeyNum=4: 15s采样 每3个TIMER6周期执行]

    I[中断优先级配置] --> J[TIMER4: 抢占优先级1 响应优先级3]
    I --> K[TIMER6: 抢占优先级2 响应优先级1]
    I --> L[SysTick: 优先级0 最高]
```

## 8. 定时器状态转换图

```mermaid
stateDiagram-v2
    [*] --> 系统初始化
    系统初始化 --> 定时器禁用状态: all_timer_init完成

    定时器禁用状态 --> 定时器运行状态: 按键触发或START命令
    定时器运行状态 --> 定时器禁用状态: 按键触发或STOP命令

    state 定时器运行状态 {
        [*] --> TIMER4运行
        [*] --> TIMER6运行

        TIMER4运行 --> LED闪烁: 500ms中断
        LED闪烁 --> TIMER4运行

        TIMER6运行 --> 周期判断: 5s基础中断
        周期判断 --> 设置标志位: KeyNum匹配
        周期判断 --> 等待下次: KeyNum不匹配
        设置标志位 --> TIMER6运行
        等待下次 --> TIMER6运行
    }

    state 主任务循环 {
        [*] --> 调度器运行
        调度器运行 --> 检查ADC标志: adc_process_task
        检查ADC标志 --> 处理ADC数据: 标志位=1
        检查ADC标志 --> 调度器运行: 标志位=0
        处理ADC数据 --> 清除标志位
        清除标志位 --> 调度器运行
    }
```

### 定时器参数配置表

| 定时器 | ARR值 | PSC值 | 时钟源 | 计算周期 | 功能 | 中断优先级 |
|--------|-------|-------|--------|----------|------|------------|
| TIMER4 | 4999 | 11999 | APB1(120MHz) | 500ms | LED闪烁指示 | 抢占1/响应3 |
| TIMER6 | 49999 | 11999 | APB1(120MHz) | 5000ms | ADC采样触发 | 抢占2/响应1 |
| SysTick | SystemCoreClock/1000 | - | 系统时钟(240MHz) | 1ms | 系统时基 | 优先级0 |

### 采样周期控制表

| KeyNum值 | 采样周期 | TIMER6执行频率 | 说明 |
|----------|----------|----------------|------|
| 2 | 5秒 | 每个周期执行 | 基础采样频率 |
| 3 | 10秒 | 每2个周期执行 | 中等采样频率 |
| 4 | 15秒 | 每3个周期执行 | 低采样频率 |

## 9. 定时器调用流程详细分析

### 9.1 系统启动阶段

**初始化流程：**
1. **main函数启动** → 调用`all_timer_init()`
2. **TIMER4初始化** → `timer4_int_init(4999-1, 11999-1)`
   - 使能时钟：`rcu_periph_clock_enable(RCU_TIMER4)`
   - 复位定时器：`timer_deinit(TIMER4)`
   - 配置参数：预分频、周期、计数方向等
   - 使能中断：`timer_interrupt_enable(TIMER4, TIMER_INT_UP)`
   - 配置NVIC：`nvic_irq_enable(TIMER4_IRQn, 1, 3)`
   - **初始状态禁用**：`timer_disable(TIMER4)`

3. **TIMER6初始化** → `timer6_int_init(49999-1, 11999-1)`
   - 类似TIMER4的初始化流程
   - **初始状态禁用**：`timer_disable(TIMER6)`

### 9.2 任务调度阶段

**调度器运行机制：**
1. **主循环** → 持续调用`scheduler_run()`
2. **时间获取** → `get_system_ms()`基于SysTick计数
3. **任务检查** → 遍历任务数组，检查执行时间
4. **任务执行** → 调用对应的任务函数

**关键任务周期：**
- `adc_flip_task`: 5ms周期，控制定时器启停
- `btn_task`: 5ms周期，检测按键状态
- `uart_task`: 5ms周期，处理串口命令
- `adc_process_task`: 1ms周期，处理ADC数据
- `rtc_task`: 50ms周期，更新时间显示

### 9.3 定时器控制阶段

**启动条件：**
- 按键触发：`btn_task`设置`pre_KeyNum=1`
- 串口命令：`uart_task`接收"START"命令

**启动流程：**
1. `adc_flip_task`检查`pre_KeyNum`标志
2. 调用`timer_enable(TIMER4)`启动LED闪烁
3. 调用`timer_enable(TIMER6)`启动ADC采样定时

**停止流程：**
1. 按键或"STOP"命令触发
2. 调用`timer_disable(TIMER4)`停止LED闪烁
3. 调用`timer_disable(TIMER6)`停止ADC采样

### 9.4 中断处理阶段

**TIMER4中断处理：**
```c
void TIMER4_IRQHandler(void) {
    if (timer_interrupt_flag_get(TIMER4, TIMER_INT_FLAG_UP) == SET) {
        timer_interrupt_flag_clear(TIMER4, TIMER_INT_FLAG_UP);
        // LED1状态切换逻辑
        LED1_TOGGLE;
    }
}
```

**TIMER6中断处理：**
```c
void TIMER6_IRQHandler(void) {
    if (timer_interrupt_flag_get(TIMER6, TIMER_INT_FLAG_UP) == SET) {
        timer_interrupt_flag_clear(TIMER6, TIMER_INT_FLAG_UP);
        // 根据KeyNum值控制执行频率
        if (should_execute) {
            timer6_execute_flag = 1;  // 设置ADC处理标志
        }
    }
}
```

### 9.5 数据处理阶段

**ADC数据处理流程：**
1. `adc_process_task`检查`timer6_execute_flag`
2. 标志位为1时执行ADC数据处理
3. 读取ADC值并进行电压转换
4. 执行阈值检测和数据存储
5. 清除`timer6_execute_flag=0`

## 10. 关键设计特点

### 6.1 分离式设计
- **定时器初始化**与**定时器控制**分离
- 初始化时定时器处于禁用状态
- 通过任务调度动态控制定时器启停

### 6.2 标志位通信
- 使用`pre_KeyNum`标志控制定时器启停
- 使用`timer6_execute_flag`标志触发ADC处理
- 避免了中断中的复杂处理逻辑

### 6.3 可变采样周期
- TIMER6提供5s基础周期
- 通过KeyNum值实现5s/10s/15s可变采样
- 中断中只设置标志位，主任务中处理数据

### 6.4 多层次时间管理
- **SysTick**: 1ms系统时基
- **任务调度**: 1-50ms任务周期
- **定时器中断**: 500ms-5s应用周期

---

**文档生成时间：** 2025年6月19日
**项目团队：** 2025815992 安徽工业大学
**开发环境：** Keil MDK 5.06 + GD32F4xx标准库
