# GD32F470数据采集系统架构设计文档

## 项目概述

本项目是基于GD32F470VET6微控制器的数据采集与存储系统，采用分层模块化设计，实现了电压采集、数据存储、串口通信、实时显示等功能。

## 1. 系统整体架构图

```mermaid
graph TD
    %% 应用层
    A[系统主控 main.c] --> B[任务调度器 scheduler]
    
    %% 应用任务层
    B --> C1[ADC采集任务]
    B --> C2[按键处理任务]
    B --> C3[串口通信任务]
    B --> C4[RTC时钟任务]
    
    %% 硬件抽象层
    C1 --> D1[BSP硬件抽象层]
    C2 --> D1
    C3 --> D1
    C4 --> D1
    
    %% 底层驱动
    D1 --> E1[ADC驱动]
    D1 --> E2[GPIO驱动]
    D1 --> E3[USART驱动]
    D1 --> E4[Timer驱动]
    D1 --> E5[I2C驱动]
    D1 --> E6[SPI驱动]
    D1 --> E7[SDIO驱动]
    
    %% 外设组件
    E1 --> F1[电压采集]
    E2 --> F2[LED指示]
    E2 --> F3[按键输入]
    E3 --> F4[串口通信]
    E4 --> F5[定时采样]
    E5 --> F6[OLED显示]
    E6 --> F7[Flash存储]
    E7 --> F8[SD卡存储]
    
    %% 数据流
    C1 -.数据.-> G1[数据处理]
    G1 --> G2[阈值检测]
    G1 --> G3[格式转换]
    G2 --> G4[超限报警]
    G3 --> G5[数据存储]
    G5 --> F7
    G5 --> F8
    
    %% 样式定义
    classDef appLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef taskLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef bspLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef driverLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef deviceLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataFlow fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class A appLayer
    class B appLayer
    class C1,C2,C3,C4 taskLayer
    class D1 bspLayer
    class E1,E2,E3,E4,E5,E6,E7 driverLayer
    class F1,F2,F3,F4,F5,F6,F7,F8 deviceLayer
    class G1,G2,G3,G4,G5 dataFlow
```

### 架构说明：
- **应用层（蓝色）**：系统主控制器和任务调度器
- **任务层（紫色）**：各个功能任务模块
- **抽象层（绿色）**：BSP硬件抽象层，提供统一的硬件接口
- **驱动层（橙色）**：底层硬件驱动程序
- **设备层（粉色）**：具体的外设组件
- **数据流（浅绿色）**：数据处理和存储流程

## 2. 模块接口设计图

```mermaid
graph LR
    %% 核心控制模块
    MAIN[主控制器<br/>main.c] --> SCHED[调度器<br/>scheduler]
    
    %% 应用模块
    SCHED --> ADC[ADC应用<br/>adc_app]
    SCHED --> BTN[按键应用<br/>btn_app]
    SCHED --> UART[串口应用<br/>usart_app]
    SCHED --> RTC[时钟应用<br/>rtc_app]
    
    %% 硬件抽象层
    ADC --> BSP[BSP抽象层<br/>bsp_gd32f470vet6]
    BTN --> BSP
    UART --> BSP
    RTC --> BSP
    
    %% 存储模块
    ADC --> FLASH[Flash存储<br/>flash_app]
    ADC --> SD[SD卡存储<br/>sd_app]
    UART --> FLASH
    UART --> SD
    
    %% 通信组件
    UART --> RING[环形缓冲区<br/>uart_ringbuffer]
    
    %% 显示模块
    ADC --> OLED[OLED显示<br/>oled_app]
    
    %% 接口标注
    SCHED -.任务函数指针.-> ADC
    SCHED -.定时调度.-> BTN
    ADC -.标志位通信.-> UART
    BTN -.全局变量.-> ADC
    
    %% 样式定义
    classDef coreModule fill:#bbdefb,stroke:#1976d2,stroke-width:3px
    classDef appModule fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef bspModule fill:#ffecb3,stroke:#f57c00,stroke-width:2px
    classDef storageModule fill:#f8bbd9,stroke:#c2185b,stroke-width:2px
    classDef commModule fill:#d1c4e9,stroke:#7b1fa2,stroke-width:2px
    
    class MAIN,SCHED coreModule
    class ADC,BTN,UART,RTC appModule
    class BSP bspModule
    class FLASH,SD,OLED storageModule
    class RING commModule
```

### 接口设计说明：
- **核心模块（深蓝色）**：主控制器和调度器，系统的控制中心
- **应用模块（绿色）**：各功能应用模块，实现具体业务逻辑
- **抽象层（橙色）**：BSP层，提供硬件无关的操作接口
- **存储模块（粉色）**：数据存储和显示模块
- **通信组件（紫色）**：通信相关的组件模块

## 3. 模块接口交互流程图

```mermaid
sequenceDiagram
    participant M as 主控制器
    participant S as 调度器
    participant B as 按键模块
    participant A as ADC模块
    participant U as 串口模块
    participant F as Flash模块
    participant SD as SD卡模块
    
    M->>S: 系统初始化
    S->>S: 任务注册
    
    loop 主循环
        S->>B: 调用btn_task()
        B->>B: 检测按键状态
        alt 按键按下
            B->>A: 设置pre_KeyNum标志
        end
        
        S->>A: 调用adc_flip_task()
        A->>A: 检查pre_KeyNum标志
        alt 启动采样
            A->>A: 启动定时器
            Note over A: timer6_execute_flag=1
        end
        
        S->>A: 调用adc_process_task()
        A->>A: 检查timer6_execute_flag
        alt 需要处理数据
            A->>A: ADC数据转换
            A->>F: 存储配置参数
            A->>SD: 存储采样数据
            A->>U: 输出串口数据
        end
        
        S->>U: 调用uart_task()
        U->>U: 处理串口命令
        alt 配置命令
            U->>F: 更新Flash配置
            U->>SD: 记录操作日志
        end
    end
```

### 交互流程说明：
1. **系统初始化**：主控制器启动调度器并注册所有任务
2. **任务调度**：调度器按照设定周期调用各个任务函数
3. **模块通信**：通过全局变量、标志位等方式实现模块间通信
4. **数据处理**：ADC模块采集数据后进行处理和存储
5. **命令处理**：串口模块处理外部命令并更新系统配置

## 4. 核心接口设计分析

### 4.1 调度器接口设计

**任务结构体定义：**
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 执行周期（毫秒）
    uint32_t last_run;         // 上次运行时间
} task_t;
```

**公共接口：**
```c
void scheduler_init(void);     // 调度器初始化
void scheduler_run(void);      // 调度器运行
```

**设计特点：**
- 统一的任务函数签名：`void task_name(void)`
- 基于时间片的非抢占式调度
- 静态任务配置，编译时确定任务数量

### 4.2 硬件抽象层接口设计

**LED控制接口：**
```c
#define LED1_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED1_PIN; else GPIO_BC(LED_PORT) = LED1_PIN; } while(0)
#define LED1_TOGGLE     do { GPIO_TG(LED_PORT) = LED1_PIN; } while(0)
#define LED1_ON         do { GPIO_BOP(LED_PORT) = LED1_PIN; } while(0)
#define LED1_OFF        do { GPIO_BC(LED_PORT) = LED1_PIN; } while(0)
```

**按键读取接口：**
```c
#define KEY1_READ       gpio_input_bit_get(KEYE_PORT, KEY1_PIN)
#define KEY2_READ       gpio_input_bit_get(KEYE_PORT, KEY2_PIN)
```

**初始化接口：**
```c
void bsp_led_init(void);       // LED初始化
void bsp_btn_init(void);       // 按键初始化
void bsp_oled_init(void);      // OLED初始化
void bsp_usart_init(void);     // 串口初始化
void bsp_adc_init(void);       // ADC初始化
```

### 4.3 通信模块接口设计

**串口通信接口：**
```c
int my_printf(uint32_t usart_periph, const char *format, ...);  // 格式化输出
void parse_uart_command(uint8_t *buffer, uint16_t length);      // 命令解析
void uart_task(void);                                           // 串口任务
```

**环形缓冲区接口：**
```c
void uart_ringbuffer_init(void);                               // 初始化
uint32_t uart_ringbuffer_read(uint8_t *buffer, uint32_t size); // 读取数据
uint32_t uart_ringbuffer_available(void);                      // 可用数据长度
```

### 4.4 数据存储接口设计

**Flash存储接口：**
```c
uint8_t flash_write_data(uint32_t addr, uint8_t *data, uint16_t len);  // 写入数据
uint8_t flash_read_data(uint32_t addr, uint8_t *data, uint16_t len);   // 读取数据
void flash_ratio_stored(float ratio_value);                            // 存储变比
float flash_ratio_read(void);                                          // 读取变比
void flash_threshold_stored(float threshold_value);                    // 存储阈值
float flash_threshold_read(void);                                      // 读取阈值
```

**SD卡存储接口：**
```c
void sd_fatfs_init(void);                          // SD卡初始化
void store_sample_data(float voltage);             // 存储采样数据
void store_overlimit_data(float voltage);          // 存储超限数据
void store_log_entry(const char *action);          // 存储日志
void store_hide_data(uint32_t timestamp, float voltage);  // 存储加密数据
```

## 5. 模块间通信机制

### 5.1 全局变量通信
```c
extern uint8_t KeyNum;                    // 按键编号（采样周期控制）
extern uint8_t pre_KeyNum;                // 按键触发标志
extern volatile uint8_t timer6_execute_flag;  // 定时器执行标志
extern uint8_t sd_init_success;           // SD卡初始化状态
extern uint8_t encrypt_mode_enabled;      // 加密模式状态
extern float input_radio;                 // 输入变比
extern float input_threshold;             // 输入阈值
```

### 5.2 标志位通信机制
- **timer6_execute_flag**：定时器中断设置，ADC任务检查
- **pre_KeyNum**：按键任务设置，ADC任务检查
- **rx_flag**：串口中断设置，串口任务检查

### 5.3 函数回调机制
- 调度器通过函数指针调用各任务函数
- 中断服务程序通过标志位触发任务执行

## 6. 接口设计优势

### 6.1 分层抽象
- **应用层**：专注业务逻辑
- **抽象层**：提供硬件无关接口
- **驱动层**：封装硬件操作细节

### 6.2 统一规范
- 统一的函数命名规范
- 统一的初始化接口
- 统一的任务函数签名

### 6.3 低耦合设计
- 模块间通过标准接口通信
- 硬件相关操作集中在BSP层
- 配置参数统一管理

### 6.4 可扩展性
- 新增任务只需添加到调度器数组
- 新增硬件只需扩展BSP接口
- 支持运行时配置修改

## 7. 系统特性

### 7.1 实时性
- 基于定时器的精确采样
- 非阻塞的任务调度机制
- 中断驱动的数据处理

### 7.2 可靠性
- 多重数据存储备份
- 完整的错误处理机制
- 系统状态监控和日志记录

### 7.3 灵活性
- 支持多种采样周期配置
- 支持加密和非加密模式
- 支持串口命令动态配置

---

**文档生成时间：** 2025年6月19日
**项目团队：** 2025815992 安徽工业大学
**开发环境：** Keil MDK 5.06 + GD32F4xx标准库
