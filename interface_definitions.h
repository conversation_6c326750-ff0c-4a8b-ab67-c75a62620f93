/**
 * @file interface_definitions.h
 * @brief GD32F470数据采集系统接口定义文档
 * <AUTHOR> 安徽工业大学
 * @date 2025-06-19
 */

#ifndef INTERFACE_DEFINITIONS_H
#define INTERFACE_DEFINITIONS_H

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

//=============================================================================
// 1. 调度器接口定义
//=============================================================================

/**
 * @brief 任务结构体定义
 */
typedef struct {
    void (*task_func)(void);    ///< 任务函数指针
    uint32_t rate_ms;          ///< 执行周期（毫秒）
    uint32_t last_run;         ///< 上次运行时间
} task_t;

/**
 * @brief 调度器初始化
 */
void scheduler_init(void);

/**
 * @brief 调度器运行
 */
void scheduler_run(void);

//=============================================================================
// 2. 硬件抽象层接口定义
//=============================================================================

/**
 * @brief BSP初始化接口
 */
void bsp_led_init(void);       ///< LED初始化
void bsp_btn_init(void);       ///< 按键初始化
void bsp_oled_init(void);      ///< OLED初始化
void bsp_usart_init(void);     ///< 串口初始化
void bsp_adc_init(void);       ///< ADC初始化
void bsp_gd25qxx_init(void);   ///< SPI Flash初始化

/**
 * @brief LED控制宏定义
 */
#define LED1_ON         do { GPIO_BOP(LED_PORT) = LED1_PIN; } while(0)
#define LED1_OFF        do { GPIO_BC(LED_PORT) = LED1_PIN; } while(0)
#define LED1_TOGGLE     do { GPIO_TG(LED_PORT) = LED1_PIN; } while(0)
#define LED1_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED1_PIN; else GPIO_BC(LED_PORT) = LED1_PIN; } while(0)

/**
 * @brief 按键读取宏定义
 */
#define KEY1_READ       gpio_input_bit_get(KEYE_PORT, KEY1_PIN)
#define KEY2_READ       gpio_input_bit_get(KEYE_PORT, KEY2_PIN)
#define KEY3_READ       gpio_input_bit_get(KEYB_PORT, KEY3_PIN)

//=============================================================================
// 3. 应用任务接口定义
//=============================================================================

/**
 * @brief ADC应用接口
 */
void adc_flip_task(void);                    ///< ADC采样控制任务
void adc_process_task(void);                 ///< ADC数据处理任务
uint32_t voltage_to_hex(float voltage);     ///< 电压值转HEX格式

/**
 * @brief 按键应用接口
 */
void btn_task(void);                         ///< 按键处理任务

/**
 * @brief 串口应用接口
 */
void uart_task(void);                                           ///< 串口任务
int my_printf(uint32_t usart_periph, const char *format, ...); ///< 格式化输出
void parse_uart_command(uint8_t *buffer, uint16_t length);      ///< 命令解析

/**
 * @brief RTC应用接口
 */
void rtc_task(void);                         ///< RTC时钟任务
void rtc_show_time(void);                    ///< 显示当前时间
void rtc_adcshow_time(void);                 ///< ADC采样时显示时间

//=============================================================================
// 4. 存储模块接口定义
//=============================================================================

/**
 * @brief Flash存储接口
 */
uint8_t flash_write_data(uint32_t addr, uint8_t *data, uint16_t len);  ///< 写入数据
uint8_t flash_read_data(uint32_t addr, uint8_t *data, uint16_t len);   ///< 读取数据
void flash_ratio_stored(float ratio_value);                            ///< 存储变比
float flash_ratio_read(void);                                          ///< 读取变比
void flash_threshold_stored(float threshold_value);                    ///< 存储阈值
float flash_threshold_read(void);                                      ///< 读取阈值
void power_on_count_init(void);                                        ///< 初始化上电次数
uint32_t power_on_count_read(void);                                    ///< 读取上电次数

/**
 * @brief SD卡存储接口
 */
void sd_fatfs_init(void);                                      ///< SD卡初始化
void store_sample_data(float voltage);                         ///< 存储采样数据
void store_overlimit_data(float voltage);                      ///< 存储超限数据
void store_log_entry(const char *action);                      ///< 存储日志
void store_hide_data(uint32_t timestamp, float voltage);       ///< 存储加密数据
void reset_data_storage_system(void);                          ///< 重置数据存储系统

//=============================================================================
// 5. 通信模块接口定义
//=============================================================================

/**
 * @brief 环形缓冲区接口
 */
void uart_ringbuffer_init(void);                               ///< 初始化
uint32_t uart_ringbuffer_read(uint8_t *buffer, uint32_t size); ///< 读取数据
uint32_t uart_ringbuffer_available(void);                      ///< 可用数据长度
void uart_ringbuffer_irq_handler(void);                        ///< 中断处理

//=============================================================================
// 6. 显示模块接口定义
//=============================================================================

/**
 * @brief OLED显示接口
 */
void OLED_Init(void);                                          ///< OLED初始化
void OLED_Clear(void);                                         ///< 清屏
void oled_printf(uint8_t x, uint8_t y, const char *format, ...); ///< 格式化显示

//=============================================================================
// 7. 全局变量接口定义
//=============================================================================

/**
 * @brief 系统状态变量
 */
extern uint8_t KeyNum;                          ///< 按键编号（采样周期控制）
extern uint8_t pre_KeyNum;                      ///< 按键触发标志
extern volatile uint8_t timer6_execute_flag;    ///< 定时器执行标志
extern uint8_t sd_init_success;                 ///< SD卡初始化状态
extern uint8_t encrypt_mode_enabled;            ///< 加密模式状态
extern uint8_t oled_flag;                       ///< OLED显示标志

/**
 * @brief 配置参数变量
 */
extern float input_radio;                       ///< 输入变比
extern float input_threshold;                   ///< 输入阈值
extern uint32_t power_on_count;                 ///< 上电次数计数器
extern uint16_t global_data_count;              ///< 全局数据计数器

//=============================================================================
// 8. 系统配置定义
//=============================================================================

/**
 * @brief 系统配置常量
 */
#define DEBUG_USART                 USART0          ///< 调试串口
#define UART_RX_BUFFER_SIZE         1024           ///< 串口接收缓冲区大小
#define Init_Key                    2              ///< 初始按键值
#define Device_ID_ADDR              0x08000000     ///< 设备ID地址

/**
 * @brief 任务调度周期定义
 */
#define ADC_FLIP_TASK_PERIOD        5              ///< ADC控制任务周期(ms)
#define BTN_TASK_PERIOD             5              ///< 按键任务周期(ms)
#define UART_TASK_PERIOD            5              ///< 串口任务周期(ms)
#define RTC_TASK_PERIOD             50             ///< RTC任务周期(ms)
#define ADC_PROCESS_TASK_PERIOD     1              ///< ADC处理任务周期(ms)

//=============================================================================
// 9. 错误码定义
//=============================================================================

/**
 * @brief 系统错误码
 */
typedef enum {
    SYSTEM_OK = 0,                  ///< 操作成功
    SYSTEM_ERROR,                   ///< 一般错误
    SYSTEM_TIMEOUT,                 ///< 超时错误
    SYSTEM_INVALID_PARAM,           ///< 参数错误
    SYSTEM_NOT_INITIALIZED,         ///< 未初始化错误
    SYSTEM_STORAGE_ERROR,           ///< 存储错误
    SYSTEM_COMMUNICATION_ERROR      ///< 通信错误
} system_error_t;

//=============================================================================
// 10. 回调函数类型定义
//=============================================================================

/**
 * @brief 回调函数类型定义
 */
typedef void (*task_callback_t)(void);          ///< 任务回调函数
typedef void (*error_callback_t)(system_error_t error); ///< 错误回调函数

#ifdef __cplusplus
}
#endif

#endif /* INTERFACE_DEFINITIONS_H */

/**
 * @brief 接口设计说明
 * 
 * 本系统采用分层模块化设计，主要包含以下几个层次：
 * 
 * 1. 应用层：main.c 和 scheduler 模块
 *    - 负责系统初始化和任务调度
 *    - 提供统一的任务管理接口
 * 
 * 2. 任务层：各个应用任务模块
 *    - ADC采集任务：负责电压采集和数据处理
 *    - 按键任务：负责按键检测和状态切换
 *    - 串口任务：负责命令解析和数据输出
 *    - RTC任务：负责时间管理和显示
 * 
 * 3. 抽象层：BSP硬件抽象层
 *    - 提供硬件无关的操作接口
 *    - 统一的初始化和控制接口
 * 
 * 4. 存储层：Flash和SD卡存储模块
 *    - 配置参数的持久化存储
 *    - 采样数据和日志的文件存储
 * 
 * 5. 通信层：串口通信和环形缓冲区
 *    - 可靠的数据传输机制
 *    - 命令解析和响应处理
 * 
 * 模块间通信机制：
 * - 全局变量：用于状态共享
 * - 标志位：用于异步事件通知
 * - 函数回调：用于任务调度
 * - 接口函数：用于功能调用
 */
